<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.jcy</groupId>
  <artifactId>underplatformprd</artifactId>
  <packaging>pom</packaging>
  <version>V1.0.0-SNAPSHOT</version>
  <properties>
    <zz.project.version>3.0.0-SNAPSHOT</zz.project.version>
    <zz.tool.version>4.0.0-SNAPSHOT</zz.tool.version>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <maven.plugin.version>3.8.1</maven.plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring.boot.version>2.7.1</spring.boot.version>
    <lombok.version>1.18.22</lombok.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <flowable.version>6.7.2</flowable.version>
    <spring.platform.version>Cairo-SR8</spring.platform.version>
    <!-- 推荐使用Harbor -->
    <docker.registry.url>*************</docker.registry.url>
    <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
    <docker.username>zz</docker.username>
    <docker.password>zz</docker.password>
    <docker.namespace>zz</docker.namespace>
    <docker.plugin.version>1.4.13</docker.plugin.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.zz</groupId>
        <artifactId>zz-starter-serialno</artifactId>
        <version>${zz.tool.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.zz</groupId>
            <artifactId>zz-core-cloud</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.zz.platform</groupId>
        <artifactId>zz-bom</artifactId>
        <exclusions>
          <exclusion>
            <groupId>com.zz</groupId>
            <artifactId>zz-core-cloud</artifactId>
          </exclusion>
        </exclusions>
        <version>${zz.tool.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.spring.platform</groupId>
        <artifactId>platform-bom</artifactId>
        <version>${spring.platform.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <!-- lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- 测试依赖 -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Mockito 测试依赖 -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring.boot.version}</version>
          <configuration>
            <finalName>${project.build.finalName}</finalName>
          </configuration>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>com.spotify</groupId>
          <artifactId>dockerfile-maven-plugin</artifactId>
          <version>${docker.plugin.version}</version>
          <configuration>
            <username>${docker.username}</username>
            <password>${docker.password}</password>
            <repository>${docker.registry.url}/${docker.namespace}/${project.build.finalName}</repository>
            <tag>${project.version}</tag>
            <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
            <buildArgs>
              <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
            </buildArgs>
          </configuration>
          <!--新增如下配置，运行 mvn deploy 命令便会自动打包镜像-->
          <!--<executions>
                        <execution>
                            <id>default</id>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>-->
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-archetype-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven.plugin.version}</version>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
              <path>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
              </path>
            </annotationProcessorPaths>
            <compilerArgs>
              <arg>-parameters</arg>
            </compilerArgs>
            <source>8</source>
            <target>8</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M7</version>
          <configuration>
            <useSystemClassLoader>false</useSystemClassLoader>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <distributionManagement>
    <repository>
      <id>releases</id>
      <url>http://123.56.46.142:8081/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <url>http://123.56.46.142:8081/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <modules>
    <module>zz-server</module>
    <module>permissiongrp</module>
  </modules>
</project>
