---
type: "manual"
---

# 项目结构规范

## 重要规范

- 在根据项目结构生成完整的项目后, 除非显式下达更改项目结构命令, 不要对项目结构进行修改.
- 首先检查当前工作区是否在以产品名称命名的工作区下，若工作区不存在，则创建以产品名称命名的工作区，并进入该目录，
- 创建根目录pom注意！！！根目录pom一定要在以产品命名的文件夹下 
- 按照用户需求和提示规范进行项目结构目录的生成
- 创建结构的时候请你严格遵守规范，把项目、分组、上下文对应的目录结构创建出来
- 产品、分组及上下文完整创建后, 在zz-server模块中引入所有上下文的northbound-remote模块以及southbound-adapter模块

## 概念识别

- 产品/项目  
这个概念指的是整个工程, 我们的所有工作都是在这个限定范围内展开的. 在进行结构生成时, 产品/项目需要加上prd后缀, 例如dingdangmallprd.  
- 分组  
对于一些中间层的限界上下文, 其并没有对应任何实际代码, 只是对多个限界上下文进行封装分组, 我们将其称为分组, 在项目命名上我们以grp为后缀.
- 限界上下文  
在产品/项目下细分的概念, 这是领域驱动设计中的概念, 一个限界上下文对应一个Maven项目, 在项目命名和包命名上我们以bc为后缀.  
- 用例
在进行用例拆分时, 我们根据对数据的操作分为了命令用例和查询用例.用例对应的包以biz为后缀.  
- 聚合  
在限界上下文内部, 我们划分了聚合, 这是领域驱动设计中的概念, 在包命名上以aggr为后缀.  
- 其他定义
业务服务就是系统用例，应用服务就是实现用例，应用组件就是上下文模块

## 生成代码模块结构使用maven命令

1. 创建产品初始模块：根据项目整体规范，填充以下命令并使用, 不需要提前创建文件夹, 不要指定任何上下文, 如果在Power Shell环境下, 参数值需要使用单引号包裹, 该命令会创建对应产品文件夹，无需提前创建产品文件夹：
  mvn -s {maven-setting-path} archetype:generate -DgroupId='com.zz' -DartifactId='产品名称' -Dversion='3.0.0-SNAPSHOT' -Dpackage='包名' -DarchetypeGroupId='com.zz' -DarchetypeArtifactId='zz-rhombus-project-archetype' -DarchetypeVersion='3.0.0-SNAPSHOT' -DinteractiveMode=false
2. 创建分组模块：根据项目整体规范，填充以下命令并使用, 父级指定为产品名称, 生成的结构完全符合DDD规范且是完整的DDD上下文结构, 无需关心子模块内容, 如果没有在产品文件夹下，先进入产品文件夹再进行创建. 如果在Power Shell环境下, 参数值需要使用单引号包裹: 
  mvn -s {maven-setting-path} archetype:generate -DgroupId='com.zz' -DartifactId='分组名称' -Dversion='3.0.0-SNAPSHOT' -Dpackage='包名' -DarchetypeGroupId='com.zz' -DarchetypeArtifactId='zz-rhombus-group-archetype'  -Dparent-version='3.0.0-SNAPSHOT' -Dparent-artifactId='产品名称' -Dparent-groupId='com.zz' -DinteractiveMode=false
3. 创建上下文模块：根据项目整体规范，填充以下命令并使用, 父级指定为产品名称或分组名称, 生成的结构完全符合DDD规范且是完整的DDD上下文结构, 无需关心子模块内容, 如果上下文层级是定义在分组文件夹下，请先进入对应的分组文件夹下再进行创建. 如果在Power Shell环境下, 参数值需要使用单引号包裹:
  mvn -s {maven-setting-path} archetype:generate -DgroupId='com.zz' -DartifactId='上下文名称' -Dversion='3.0.0-SNAPSHOT' -Dpackage='包名' -Dparent-artifactId='产品名称/分组名称' -Dparent-groupId='com.zz' -Dparent-version='3.0.0-SNAPSHOT' -DarchetypeGroupId='com.zz' -DarchetypeArtifactId='zz-rhombus-module-archetype' -DinteractiveMode=false
4. 产品、分组及上下文完整创建后, 在zz-server模块中引入所有上下文的northbound-remote模块以及southbound-adapter模块


## 代码注释

代码的注释非常重要, 时刻注意JavaDoc注释规范生成注释

1. 生成的类上都要有注释，例如：

```java
/**
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: ${DATE}<p>
 * Time: ${TIME}<p>
 * ================================
*/
```

2. 不同的逻辑块之间要有注释.
3. 注释的原则是简洁清晰易懂且有用, 如果不需要注释的地方请不要添加注释.
4. 需要在终端执行的命令, 根据不同命令格式来调整, 所有的命令单条执行, 不要多条命令拼接.
5. 生成业务代码的重要逻辑需要有解析的注释，例如：

```java
//1、校验用户token，获取当前授权的用户Id
TODO 代码段
//2、根据用户Id，获取用户信息
TODO 代码段
```

## 项目模块结构

### 项目模块结构举例

- 项目大概结构的示例:

项目/                               # 项目定层包（productName），后缀prd
├── 分组1/                          # 分组（groupName），后缀grp
│   ├── 限界上下文1/                # 上下文基础服务模块，后缀bc
│   └── 限界上下文2/                # 上下文基础服务模块，后缀bc
├── 限界上下文3/                    # 上下文基础服务模块，后缀bc
├── 限界上下文4/                    # 上下文基础服务模块，后缀bc
└── zz-server/                      # 启动入口
注意：整个项目只有一个zz-server!!! 没有上下文-server这种形式

- 一个上下文展开的示例:

productName/  # 项目定层包（productName），后缀prd
├── 分组1/  # 分组（groupName），后缀grp
│   ├── orderbc/  # 举例的订单上下文模块
│   │   ├── orderbc-client  # 客户端模块
│   │   ├── orderbc-domain  # 领域模块
│   │   ├── orderbc-northbound  # 北向网关模块
│   │   │   ├── orderbc-northbound-local  # 北向网关-本地网关模块
│   │   │   └── orderbc-northbound-remote  # 北向网关-远程网关模块
│   │   ├── orderbc-southbound  # 南向网关
│   │   └── orderbc-southbound-adapter  # 南向网关模块, 提供技术实现
│   └── 限界上下文2/  # 上下文模块，后缀bc
├── 限界上下文3/  # 上下文模块，后缀bc
├── 限界上下文4/  # 上下文模块，后缀bc
└── zz-server/  # 启动入口

### 项目依赖关系

remote -> client
remote -> local
local -> domain
adapter -> domain
adapter -> local

### 依赖相关

1. 领域相关的接口依赖在领域层引入  

```xml
<!--值对象、领域对象依赖-->
<dependency>
    <groupId>com.zz</groupId>
    <artifactId>zz-core-ddd</artifactId>
    <version>4.0.0-SNAPSHOT</version>
</dependency>
<!--错误码依赖-->
<dependency>
    <groupId>com.zz</groupId>
    <artifactId>zz-core-tool</artifactId>
    <version>4.0.0-SNAPSHOT</version>
</dependency>
<!--数据库对象依赖-->
<dependency>
    <groupId>com.zz</groupId>
    <artifactId>zz-starter-tenant</artifactId>
    <version>4.0.0-SNAPSHOT</version>
</dependency>
```

### 工具类库

- Hutool 5.8.27
- Lombok, 能够使用lombok注解的地方, 尽量使用lombok, 例如@Data, @Getter
- MapStruct, 类型转换工具, 在Assembler或Converter上使用@Mapper(uses = {XxxMapping.class}).
- Mybatis Plus


## 注意事项

### 生成代码校验

- 代码必须引入正确的package包路径！！！
- package-info.java的文件需要写入对应当前文件所在的正确包路径！！！
- 生成代码前必须先校验有没有对应包路径目录，如果有则复用。
- 生成代码前必须先校验有没有对应包路径下的类文件，如果有则复用。


### 包命名规范

- 统一前缀: com.zz.项目名prd.
- 对于中间级的grp不需要在包名中体现.
- 模块包: com.zz.项目名prd.[module-name]bc.

### 类命名规范

- 聚合根实体: XxxAggregateRootEntity
- 值对象: Xxx(直接使用)
- 领域服务: XxxDomainService
- 验证器: Xxx(业务操作, 如增删改查, 每个操作对应一个校验器)Validator, 例如XxxCreateValidator
- 领域事件: XxxDomainEvent
- 命令仓储接口: XxxCommandRepository
- 资源网关接口: XxxResourceGateway
- 错误码: XxxResultCode
- 值对象与基础类型映射逻辑: XxxMapping
- 领域事件发布器: XxxDomainEventPublisher
- 命令用例应用服务: XxxCommandUseCaseAppService
- 查询用例应用服务: XxxQueryUseCaseAppService
- 查询仓储接口: XxxQueryRepository
- 客户端接口: XxxClient
- 数据库对象: XxxDO
- 装配器接口: XxxAssembler(北向网关-本地网关, 负责将pl中的请求响应对象转换为领域实体)
- 转换器接口: XxxConverter(南向网关-适配器层, 负责数据库对象与其他对象的转换)
- 应用事件发布器: XxxAppEventPublisher(北向网关-本地网关, 负责将领域事件发布到应用层)
- 领域事件订阅者: XxxDomainEventHandler(南向网关-适配器层, 读模型使用, 负责订阅领域事件)
- 应用事件订阅者: XxxAppEventSubscriber(北向网关-远程网关, 负责订阅应用事件)
- 上下文模块的pl发布语言层存放着这个上下文的请求和响应对象，请求对象结构为：xxxRequest，响应对象有3种结构类型：xxResult（调用命令职责返回的结果）、xxxResponse（调用当前上下文client能力后返回的结果）、xxxxView（调用查询职责获取的结果）


## 结果校验

生成后需要进行以下校验
- [ ] 生成新的目录、文件、代码内容后，需要校验生成内容是否符合注意事项
