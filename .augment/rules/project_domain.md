---
type: "manual"
description: "globs:"
---
# 领域对象规范

## 注意事项

- 实体必须包含唯一标识, 根据实际情况选择继承AggregateRoot<XxxId>、BaseEntity<XxxId>、BaseTenantEntity<XxxId>、TenantAggregateRootEntity<XxxId>.
- 每个实体都要有一个ID值对象, XxxId, 封装了Long类型的value, 在继承1中的接口时填写到泛型中, 作为技术序列号使用.
- 一般的, 每个实体都要有一个XxxSN值对象, 封装了String类型的value, 作为业务序列号使用.
- 值对象是对基本类型的封装, 且封装的值必须是不可变的, 构造方法自行实现, 需要对值进行非空校验, 必须实现ValueObject接口并实现sameValueAs接口.
- 值对象不需要静态的of方法, 直接使用构造器进行初始化.
- 值对象是单独的一个文件, 不以内部类的形式出现. 
- 领域服务只包含领域逻辑, 不包含业务逻辑.
- 命令仓储接口定义在领域层, 查询仓储接口定义在应用层.
- 校验器负责对领域实体进行校验, 根据业务操作来拆分校验器, 每一种操作对应一个校验器. 必要情况下可以使用注入仓储, 必须继承AbstractValidatore抽象类并实现bool validate方法.
- 领域实体的成员都是值对象, 不可使用基本类型, 必须使用值对象, 无需使用final修饰.
- 领域实体的成员变量必须使用private修饰, 并提供public的get方法, 不要提供set方法.
- 使用Mapping类进行值对象和基础类型映射逻辑编写, 并在Mapstruct需要时使用(在Assembler或Converter中@Mapper(uses = {XxxMapping.class})).
- 领域对象不包含领域逻辑, 所有的领域逻辑必须写在领域服务中.
- 领域对象不能直接new或者修改成员, 必须从pl或DO转换而来.
- 领域服务不负责真正存储, 而是修改领域实体的状态(toNew、toUpdate、toDelete这三个方法继承自父类), 全部调用命令仓储的store统一入口, 并且每个接口都应该使用校验器预先校验.
- 错误码需要实现IResultCode接口, 接口有三个抽象方法为int getCode() String getMessage() String getErrorCode(), 使用@Getter注解实现这三个方法
- 领域相关的导包为:
  - import com.zz.core.ddd.base.BaseEntity;
  - import com.zz.core.tool.utils.ZzKits;
  - import com.zz.starter.serialno.template.SerialNoGeneratorTemplate;
  - import com.zz.core.ddd.base.ValueObject;
  - import com.zz.starter.log.exception.ServiceException;
  - import lombok.experimental.SuperBuilder;
  - import com.zz.core.ddd.validator.AbstractValidator;

## 重要规范

1. 充分利用已有的项目结构, 禁止创建不必要的项目结构目录或文件.

## 生成后的检验清单

- [ ] 生成新的目录、文件、代码内容后，需要校验生成内容是否符合注意事项
- [ ] 聚合根类是否以AggregateRoot结尾
- [ ] 值对象类是否实现ValueObject接口
- [ ] 事件类是否继承BaseDomainEvent
- [ ] 错误码枚举是否包含异常分类标识
- [ ] 映射类是否标注@Mappe
- [ ] ...

## 包名结构

- 聚合分包需要增加后缀aggr
- 在聚合下分domainevent(可选, 存放领域事件)、domainservicevalidator(必须, 存放领域校验器)、port(必须, 存放命令仓储接口、领域事件发布器接口、资源网关接口)、valueobject(必须, 存放值对象)

## 示例参考

### 当前模块分层规范

```
com.zz.dingdangmallprd.orderbc.domain
├── orderaggr                          # 订单聚合根分包
│    ├── domainevent                    # 领域事件定义 (按需使用)
│    │   └── OrderPlacedDomainEvent.java          # （领域事件本体）
│    │
│    ├── domainservicevalidator         # 领域服务校验器
│    │   └── PlaceOrderValidator.java             # （业务校验规则容器）
│    │
│    ├── port                           # 端口接口层
│    │   ├── OrderCommandRepository.java          # 命令仓储接口
│    │   ├── OrderDomainEventPublisher.java       # 领域事件发布接口
│    │   └── OrderResourceGateway.java            # 资源网关接口
│    │
│    ├── valueobject                    # 值对象集合
│    │   ├── OrderSN.java                         # 订单SN值对象  
│    │   └── ...                                 # (其他业务值对象)
│    │
│    ├── OrderAggregateRootEntity.java           # (聚合根核心实体)
│    ├── OrderDomainService.java                # (领域服务实现) 
│    ├── OrderMapping.java                      # (值对象映射转换器) 
│    └── OrderResultCode.java                   # (领域错误码枚举)
└── package-info.java                # 包说明文件

```

### 当前模块下的代码内容示例

#### 聚合根的代码示例

```java
/**
 * 订单聚合根
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:20<p>
 * ================================
 */
@Getter
@SuperBuilder
@Setter(AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class OrderAggregateRootEntity extends BaseEntity<OrderId> {
    /**
     * 订单业务Id
     */
    private OrderSN orderSN;
    /**
     * 订单金额
     */
    private OrderMoney orderMoney;
    /**
     * 客户
     */
    private Customer customer;
    /**
     * 下单时间
     */
    private PlaceOrderTime placeOrderTime;
    /**
     * 订单商品
     */
    private List<OrderGoods> orderGoods;
    /**
     * 订单状态
     */
    private OrderStatus orderStatus;

    /**
     * 构建消费客户信息
     */
    public void completeCustomerInfo(Customer customer) {
        // 模拟从token中获取用户信息
        this.customer = customer;
    }

    /**
     * 构建新订单
     */
    @Override
    public void toNew() {
        super.toNew();
        // 默认订单状态为待支付
        this.orderStatus = OrderStatus.PENDING_PAYMENT;
        // 订单下单时间
        this.placeOrderTime = new PlaceOrderTime(LocalDateTime.now());
        // 生成订单业务编码
        this.orderSN = new OrderSN(SerialNoGeneratorTemplate.get().generateSerialNo());
    }

    /**
     * 计算订单金额
     */
    public void calculateOrderMoney() {
        BigDecimal totalMoney = BigDecimal.ZERO;
        // 循环计算订单商品总金额
        for (OrderGoods item : orderGoods) {
            totalMoney = totalMoney.add(item.getGoods().getSalePrice().multiply(BigDecimal.valueOf(item.getOrderGoodsCount())));
        }
        this.orderMoney = new OrderMoney(totalMoney);
    }

    /**
     * 构建订单商品
     */
    public void completeOrderGoods(List<OrderGoods> orderGoods) {
        this.orderGoods = orderGoods;
    }
}
```

#### 聚合子项类(如果需要的话)

```java
public class TestEntity extends BaseEntity<TestId> {
    // 其他值对象
}
```

#### 值对象的代码示例

```java
/**
 * 订单技术Id
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:04<p>
 * ================================
 */
@Getter
public class OrderId implements ValueObject<OrderId> {
    /**
     * 订单Id
     */
    private final Long value;

    /**
     * 构造函数
     *
     * @param value 订单Id
     */
    public OrderId(Long value) {
        if (ZzKits.isEmpty(value)) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_NOT_NULL, "订单Id不能为空");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(OrderId other) {
        return this.getValue().equals(other.getValue());
    }
}
```

#### 错误码类的代码示例

```java
/**
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/6<p>
 * Time: 17:45<p>
 * ================================
 */
@Getter
@AllArgsConstructor
public enum OrderResultCode implements IResultCode {
    ORDER_STATUS_ENUM_NOT_FOUND(400,"001-04-B-001", "订单状态枚举未找到"),
    GOODS_STOCK_NOT_ENOUGH(400,"001-04-B-002", "商品【%s】库存不足"),
    ;
    /**
     * 响应码
     */
    private final int code;

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 错误信息
     */
    private final String message;
}
```

#### Mapstruct映射关系类的示例

```java
/**
 * 值对象映射逻辑
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:21<p>
 * ================================
 */
public class OrderMapping {

    /**
     * 订单技术Id转数字
     *
     * @param orderId 订单技术Id
     * @return 订单技术Id数字
     */
    public Long toLong(OrderId orderId) {
        return ZzKits.isEmpty(orderId) ? null : orderId.getValue();
    }

    /**
     * 数字转订单技术Id
     *
     * @param orderId 订单技术Id数字
     * @return 订单技术Id
     */
    public OrderId toOrderId(Long orderId) {
        return ZzKits.isEmpty(orderId) ? null : new OrderId(orderId);
    }

    // 其他值对象的映射逻辑
}
```

#### 命令仓储接口的代码示例

```java
/**
 * 订单仓储资源库：聚合存储、聚合还原
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:23<p>
 * ================================
 */
public interface OrderCommandRepository {

    /**
     * 存储聚合根
     *
     * @param orderAggregateRootEntity 聚合根
     */
    void store(OrderAggregateRootEntity orderAggregateRootEntity);
}
```

#### 领域事件发布器接口的代码示例

```java
/**
 * 订单事件发布者
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:24<p>
 * ================================
 */
public interface OrderDomainEventPublisher {

    /**
     * 发布已下单领域事件
     *
     * @param orderPlacedDomainEvent 已下单事件
     */
    void publishOrderPlacedDomainEvent(OrderPlacedDomainEvent orderPlacedDomainEvent);
}
```

#### 资源网关接口的代码示例

```java
/**
 * 订单资源网关：调用一方库、二方库、三方库
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:24<p>
 * ================================
 */
public interface OrderResourceGateway {
    
    /**
     * 验证商品库存是否充足
     *
     * @param orderGoods 订单商品
     * @return 库存是否满足下单需求
     */
    Boolean validateGoodsStock(OrderGoods orderGoods);

    /**
     * 扣减商品库存
     *
     * @param order 订单聚合根实体
     */
    void deductGoodsStock(OrderAggregateRootEntity order);

    /**
     * 补全订单商品信息
     *
     * @param orderGoodsList 订单商品
     * @return 订单商品信息
     */
    List<OrderGoods> queryOrderGoodsList(List<OrderGoods> orderGoodsList);
}
```

#### 领域服务类的代码示例

```java
/**
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:21<p>
 * ================================
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderDomainService {
    /**
     * 订单命令仓储
     */
    private final OrderCommandRepository orderCommandRepository;

    /**
     * 资源网关
     */
    private final OrderResourceGateway orderResourceGateway;

    /**
     * 消息发布服务
     */
    private final OrderDomainEventPublisher orderDomainEventPublisher;

    /**
     * 下单
     *
     * @param orderAggregateRootEntity 订单聚合根实体
     */
    public void placeOrder(OrderAggregateRootEntity orderAggregateRootEntity) {
        // 补全订单的商品信息
        List<OrderGoods> orderGoods = this.orderResourceGateway.queryOrderGoodsList(orderAggregateRootEntity.getOrderGoods());
        orderAggregateRootEntity.completeOrderGoods(orderGoods);

        // 校验订单商品库存是否满足需求
        new PlaceOrderValidator(orderResourceGateway).validate(orderAggregateRootEntity);

        // 补全下单客户信息
        Customer customer = new Customer("549639d199634b00af49741bedb4a7e2", "张三", "12345678910"); // 模拟从token获取客户信息
        orderAggregateRootEntity.completeCustomerInfo(customer);

        // 计算订单总金额
        orderAggregateRootEntity.calculateOrderMoney();

        // 扣减商品库存
        this.orderResourceGateway.deductGoodsStock(orderAggregateRootEntity);

        // 存储订单聚合
        orderAggregateRootEntity.toNew();
        this.orderCommandRepository.store(orderAggregateRootEntity);

        // 发布已下单领域事件
        OrderPlacedDomainEvent orderPlacedDomainEvent = OrderPlacedDomainEvent.from(orderAggregateRootEntity);
        this.orderDomainEventPublisher.publishOrderPlacedDomainEvent(orderPlacedDomainEvent);
    }
}
```

#### 下单校验器的代码示例

```java
/**
 * 下单校验器
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:16<p>
 * ================================
 */
@AllArgsConstructor
public class PlaceOrderValidator extends AbstractValidator<OrderAggregateRootEntity> {

    /**
     * 订单资源网关
     */
    private final OrderResourceGateway orderResourceGateway;

    /**
     * 校验逻辑
     *
     * @param orderAggregateRootEntity 校验对象
     * @return 校验结果
     */
    @Override
    public boolean validate(OrderAggregateRootEntity orderAggregateRootEntity) {
        // 校验商品库存是否充足
        for (OrderGoods orderGoods : orderAggregateRootEntity.getOrderGoods()) {
            Boolean isStockAvailable = this.orderResourceGateway.validateGoodsStock(orderGoods);
            if (!isStockAvailable) {
                throw new ServiceException(OrderResultCode.GOODS_STOCK_NOT_ENOUGH, String.format(OrderResultCode.GOODS_STOCK_NOT_ENOUGH.getMessage(), orderGoods.getGoods().getGoodsName()));
            }
        }
        return true;
    }
}
```

#### 下单领域事件的代码示例

```java
/**
 * 订单下单事件，使用SpringEvent通知ES进行同步数据
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 20:26<p>
 * ================================
 */
@Getter
public class OrderPlacedDomainEvent extends ApplicationEvent {

    /**
     * 订单聚合根实体
     */
    private OrderAggregateRootEntity orderAggregateRootEntity;

    /**
     * 构造方法
     *
     * @param source 事件源
     */
    private OrderPlacedDomainEvent(Object source, OrderAggregateRootEntity orderAggregateRootEntity) {
        super(source);
        this.orderAggregateRootEntity = orderAggregateRootEntity;
    }

    /**
     * 构建已下单领域事件
     *
     * @param orderAggregateRootEntity 订单聚合根实体
     * @return 已下单领域事件
     */
    public static OrderPlacedDomainEvent from(OrderAggregateRootEntity orderAggregateRootEntity) {
        return new OrderPlacedDomainEvent(orderAggregateRootEntity, orderAggregateRootEntity);
    }
}
```