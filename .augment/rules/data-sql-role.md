---
type: "agent_requested"
description: "生成数据库脚本的时候生效"
---
# 数据库重要规范

##租户和审计字段
    tenant_id varchar(16) COMMENT '租户ID',
    create_user BIGINT(20) NOT NULL COMMENT '创建人',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user BIGINT(20) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    status INT NOT NULL DEFAULT 1 COMMENT '状态版本'