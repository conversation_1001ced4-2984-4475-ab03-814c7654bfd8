---
type: "agent_requested"
description: "globs:"
---
# 目标

bc_capability.md文件记录的内容为上下文的服务能力清单，包括上下文内的领域聚合、值对象、领域服务、业务服务、应用服务、client客户端接口、controller接口。
根据上下文实际内容进行更新记录。

# 最佳实践

以订单上下文为例：

```markdown
# 上下文名称

名称：订单上下文
英文：orderbc 

# 概述
上下文的能力总结概述。

# 领域聚合

##订单聚合根实体
### 实体信息
名称：订单聚合根
英文：orderAggregateRootEntiry
### 值对象信息
#### 订单SN
英文： orderSN
类型：string

##订单项实体
### 实体信息
名称：订单项
英文：orderItem
### 值对象信息
#### 商品SN
英文：goodsSN
商品的SN

#### 商品数量
英文：goodsCount
记录商品购买的数量


# 领域服务 (Domain Services)
> 位于：orderbc-domain模块

*当前模块暂无领域服务实现*

# 应用服务 (Application Services)
> 位于：orderbc-northbound-local模块

## 命令用例应用服务 (Command Use Case App Services)
### 更新商品信息


## 查询用例应用服务 (Query Use Case App Services)
### 根据商品SN查询商品详情

# 对外提供的client-客户端能力
> 位于：orderbc-client模块
## 根据商品SN查询商品详情


# 对外提供的Controller-API接口
> 位于：orderbc-northbound-remote模块
## 根据商品SN查询商品详情
## 更新商品信息


---
*注：本文档将随着代码实现的完善而持续更新* 

```