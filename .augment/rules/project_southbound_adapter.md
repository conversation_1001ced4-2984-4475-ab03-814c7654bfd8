---
type: "manual"
description: "生成南向网关模块时需要"
---
# 南向网关-适配器层（xx-southbound-adapter）规范

## 定义和注意事项

1. 实现北向网关层的业务服务的仓储接口（查询职责）和领域层的仓储接口（命令职责）
2. 命令仓储的store方法实现为: 将领域实体转换为DO, 然后根据领域实体的状态(getChangingStatue())判断状态(NEW、UPDATED、DELETED、UNCHANGED)
NEW：新建（代表数据库的insert插入操作）
UPDATED：更新（代表数据库的update更新操作）
DELETED：删除（代表数据库的delete删除操作）
UNCHANGED：无变更（无操作）
3. 查询仓储接口的所有方法实现都需要将查询出来的DO对象转换为pl发布语言层对应的响应对象
4. 资源网关实现集成外部系统
5. 使用Converter进行对象转换, 使用MapStruct框架进行, 需要转换时直接使用接口的INSTANCE进行, 无需注入
6. 数据库对象继承TenantDO
7. 一个仓储实现类只能注入一个对应的Mapper接口, 例如OrderCommandRepository只能引用OrderMapper, 不能出现其他DO的mapper
8. 资源网关调用其他上下文的client时, 不会抛出异常, 不论client逻辑如何, 一定会返回内容, 如果错误, 会将错误码内容抛出, 无需捕获异常.
9. 在Mapstruct的接口上，必须引入CommonMapping.class.
10. 对于MapStruct框架接口中的转换方法, 不需要使用@Mapping字段映射注解进行说明, 除非转换的字段需要特殊处理.避免重复定义、充分利用uses进行自动转换.
11. 可能需要的导包有
  - import com.zz.starter.mp.base.BaseDO;
  - import com.zz.core.ddd.common.mapstruct.CommonMapping;

## 重要提示

1. 充分利用已有的项目结构, 禁止创建不必要的项目结构目录或文件.

## 示例参考

### 整体包结构示例

```
com.zz.dingdangmallprd.orderbc.southbound.adapter
└── orderaggr  # 按照聚合分包
    ├── readmodel  # 读模型相关，根据需要进行添加
       ├── OrderDomainEventHandler.java  #领域事件处理器
       ├── OrderElasticSearchMapper.java  # ES仓储
       └── OrderReadModel.java  # 读模型实体
    ├── OrderAppEventPublisherAdapter.java  # 应用层事件发布器适配器
    ├── OrderDomainEventPublisherAdapter.java  # 领域层事件发布器适配器
    ├── OrderCommandRepositoryAdapter.java  # 命令仓储适配器
    ├── OrderQueryRepositoryAdapter.java  # 查询仓储适配器
    ├── OrderConverter.java  # 聚合根与数据库对象转换器
    ├── OrderDO.java  # 订单数据库对象
    ├── OrderMapper.java  # Mybatis Mapper
    ├── OrderMapper.xml  # Mybatis Mapper XML
    ├── OrderResourceGatewayAdapter.java  # 聚合资源网关适配器
    └── package-info.java
```

### 代码内容示例

#### 领域事件处理器示例

```java
/**
 * 读写分离数据同步，使用spring event来实现数据同步
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 17:28<p>
 * ================================
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderDomainEventHandler {
    /**
     * 仓储有两个，一个读(例如elasticsearch)，一个写(例如mysql)
     * 查询仓储（不是指[查询]的操作，而是对[读数据库]的操作）
     */
    private final PlaceOrderQueryRepository placeOrderQueryRepository;

    /**
     * 监听已下单领域事件
     *
     * @param orderPlacedDomainEvent 已下单领域事件
     */
    @EventListener
    public void listenOrderPlacedDomainEvent(OrderPlacedDomainEvent orderPlacedDomainEvent) {
        log.info("订单领域事件处理器：监听到已下单领域事件，开始向读模型(ES)同步数据");
        // 同步订单聚合至ES
        OrderAggregateRootEntity orderAggregateRootEntity = orderPlacedDomainEvent.getOrderAggregateRootEntity();
        placeOrderQueryRepository.syncOrderToElasticSearch(orderAggregateRootEntity);
    }
}
```

#### ES仓储示例

```java
/**
 * 订单ES Mapper接口
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 20:06<p>
 * ================================
 */
public interface OrderElasticSearchMapper extends ElasticsearchRepository<OrderReadModel, Long> {

    /**
     * 根据客户业务编号查询订单列表
     *
     * @param customerSN 客户业务编号
     * @return 订单ES列表
     */
    List<OrderReadModel> findByCustomerSN(String customerSN, Pageable pageable);

}
```

#### 订单读模型对象示例

```java
/**
 * 订单ES对象
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 20:03<p>
 * ================================
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "order")
public class OrderReadModel {
    /**
     * 订单编号
     */
    @Id
    private String orderSN;

    /**
     * 订单金额
     */
    private String orderMoney;

    /**
     * 商品信息
     */
    private String orderGoods;

    /**
     * 下单时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime placeOrderTime;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 客户业务SN
     */
    private String customerSN;

    /**
     * 客户昵称
     */
    private String customerNickName;

    /**
     * 客户手机号
     */
    private String customerPhoneNumber;

    /**
     * 订单聚合根实体转ES对象
     *
     * @param orderAggregateRootEntity 订单聚合根实体
     * @return ES对象
     */
    public static OrderReadModel from(OrderAggregateRootEntity orderAggregateRootEntity) {
        return OrderReadModel.builder()
                .orderSN(orderAggregateRootEntity.getOrderSN().getValue())
                .orderMoney(orderAggregateRootEntity.getOrderMoney().getValue().toString())
                .customerSN(orderAggregateRootEntity.getCustomer().getCustomerSN())
                .customerNickName(orderAggregateRootEntity.getCustomer().getCustomerNickName())
                .customerPhoneNumber(orderAggregateRootEntity.getCustomer().getCustomerPhoneNumber())
                .placeOrderTime(orderAggregateRootEntity.getPlaceOrderTime().getValue())
                .orderGoods(JSONObject.toJSONString(orderAggregateRootEntity.getOrderGoods()))
                .orderStatus(orderAggregateRootEntity.getOrderStatus().getValue())
                .build();
    }
}
```

#### 应用层事件发布器适配器示例

```java
/**
 * 订单下单事件发布适配器
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 17:23<p>
 * ================================
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderAppEventPublisherAdapter implements PlaceOrderAppEventPublisher {

    /**
     * RocketMQ模板
     */
    private final RocketMQTemplate rocketMqTemplate;


    /**
     * 发布已下单应用事件
     *
     * @param orderPlacedAppEvent 已下单应用事件
     */
    @Override
    public void publishOrderPlacedAppEvent(OrderPlacedAppEvent orderPlacedAppEvent) {
        // 发布已下单应用事件
        String pushData = JSONObject.toJSONString(orderPlacedAppEvent);
        this.commandAsyncSend(PlaceOrderAppEventConstant.ORDER_PLACED_APP_EVENT_TOPIC, pushData);
    }

    /**
     * 异步发送消息
     *
     * @param topic    主题
     * @param pushData 数据内容
     */
    private void commandAsyncSend(String topic, String pushData) {
        rocketMqTemplate.asyncSend(topic, pushData, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("向MQ的topic[{}]推送通知成功，数据内容:{}", topic, pushData);
            }

            @Override
            public void onException(Throwable throwable) {
                log.info("向MQ的topic[{}]推送通知成功，数据内容:{}", topic, pushData);
            }
        });
    }
}
```

#### 领域事件发布器适配器示例

```java
/**
 * 订单领域事件发布实现
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:29<p>
 * ================================
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderDomainEventPublisherAdapter implements OrderDomainEventPublisher {

    /**
     * Spring Event消息发布器
     */
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 发布已下单领域事件
     *
     * @param orderPlacedDomainEvent 已下单领域事件
     */
    @Override
    public void publishOrderPlacedDomainEvent(OrderPlacedDomainEvent orderPlacedDomainEvent) {
        log.info("发布已下单领域事件[OrderPlacedDomainEvent]");
        // 发布事件
        this.applicationEventPublisher.publishEvent(orderPlacedDomainEvent);
    }
}
```

#### 订单业务服务仓储接口（命令模式）的仓储适配器示例
```java
/**
 * 订单仓储实现：聚合存储、聚合还原
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:28<p>
 * ================================
 */
@Component
@AllArgsConstructor
public class OrderCommandRepositoryAdapter implements OrderCommandRepository {
    /**
     * 订单mapper接口
     */
    private final OrderMapper orderMapper;

    /**
     * 存储订单聚合
     *
     * @param orderAggregateRootEntity 订单聚合
     */
    @Override
    public void store(OrderAggregateRootEntity orderAggregateRootEntity) {
        OrderDO orderDO = OrderConverter.INSTANCE.toOrderDO(orderAggregateRootEntity);
        switch (orderAggregateRootEntity.getChangingStatus()) {
            case NEW:
                orderMapper.insert(orderDO);
                orderAggregateRootEntity.toUnChang();
                break;
            case UPDATED:
                orderMapper.updateById(orderDO);
                orderAggregateRootEntity.toUnChang();
                break;
            case DELETED:
                orderMapper.deleteById(orderDO);
                orderAggregateRootEntity.toUnChang();
                break;
            default:
                break;
        }
    }
}
```

#### 订单业务服务仓储接口（查询模式）的仓储适配器示例

```java
/**
 * 下单业务服务的查询仓储实现
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:29<p>
 * ================================
 */
@Slf4j
@Repository
@AllArgsConstructor
public class OrderQueryRepositoryAdapter implements PlaceOrderQueryRepository {
    /**
     * 订单ElasticSearchMapper
     */
    private final OrderElasticSearchMapper orderElasticSearchMapper;

    /**
     * 同步ElasticSearch数据
     *
     * @param orderAggregateRootEntity 订单聚合根领域对象
     */
    @Override
    public void syncOrderToElasticSearch(OrderAggregateRootEntity orderAggregateRootEntity) {
        // 同步订单数据到读模型
        OrderReadModel orderReadModel = OrderReadModel.from(orderAggregateRootEntity);
        orderElasticSearchMapper.save(orderReadModel);
        log.info("同步订单到读模型(ES)成功");
    }

    /**
     * 查询订单列表
     *
     * @return 订单视图列表
     */
    @Override
    public List<OrderListQueryView> queryOrderList(QueryOrderListRequest queryOrderListRequest) {
        // 从读模型(ES)中获取订单列表
        String customerSN = queryOrderListRequest.getCustomerSN();
        Pageable pageable = PageRequest.of(queryOrderListRequest.getPageNum(), queryOrderListRequest.getPageSize());
        Iterable<OrderReadModel> orderRealModelList = orderElasticSearchMapper.findByCustomerSN(customerSN, pageable);

        // 转换为视图
        return OrderConverter.INSTANCE.toOrderListQueryViewList(orderRealModelList);
    }
}
```

#### 聚合根与数据库对象转换器示例

```java
/**
 * 订单数据转换器
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:31<p>
 * ================================
 */
@Mapper(uses = {OrderMapping.class, CommonMapping.class})
public interface OrderConverter {
    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    /**
     * 聚合根领域对象 转 数据库对象
     *
     * @param orderAggregateRootEntity 聚合根实体
     * @return 数据库对象
     */
    @Mappings({
            @Mapping(target = "customerSN", source = "customer.customerSN"),
            @Mapping(target = "customerPhoneNumber", source = "customer.customerPhoneNumber"),
            @Mapping(target = "customerNickName", source = "customer.customerNickName"),
            @Mapping(target = "goodsList", source = "orderGoods")
    })
    OrderDO toOrderDO(OrderAggregateRootEntity orderAggregateRootEntity);

    /**
     * 数据库对象 转 聚合根领域对象
     *
     * @param orderDO 数据库对象
     * @return 聚合根实体
     */
    @Mappings({
            @Mapping(target = "orderGoods", source = "goodsList"),
            @Mapping(target = "customer", source = "orderDO", qualifiedByName = "toCustomer"),
    })
    OrderAggregateRootEntity toOrderAggregateRootEntity(OrderDO orderDO);

    /**
     * 字符串转订单客户
     *
     * @param orderDO 数据库对象
     * @return 订单客户
     */
    @Named("toCustomer")
    default Customer toCustomer(OrderDO orderDO) {
        return new Customer(orderDO.getCustomerSN(), orderDO.getCustomerNickName(), orderDO.getCustomerPhoneNumber());
    }

    /**
     * 订单ElasticSearch实体列表 转 订单placed视图列表
     *
     * @param orderRealModelList 订单ElasticSearch实体列表
     * @return 订单placed视图列表
     */
    List<OrderListQueryView> toOrderListQueryViewList(Iterable<OrderReadModel> orderRealModelList);

    /**
     * 订单ElasticSearch实体 转 订单placed视图
     *
     * @param orderReadModel 订单ElasticSearch实体
     * @return 订单placed视图
     */
    @Mappings({
            @Mapping(target = "goodsList", source = "orderGoods", qualifiedByName = "toGoodsInfoViewList")
    })
    OrderListQueryView toOrderListQueryView(OrderReadModel orderReadModel);

    /**
     * 订单商品值对象转换为商品信息
     *
     * @param orderGoods ES中订单商品列表
     * @return 商品信息
     */
    @Named("toGoodsInfoViewList")
    default List<GoodsInfoView> toGoodsInfoViewList(String orderGoods) {
        List<OrderGoods> orderGoodsList = JSONArray.parseArray(orderGoods, OrderGoods.class);
        List<GoodsInfoView> goodsInfoViewList = new ArrayList<>();
        for (OrderGoods item : orderGoodsList) {
            GoodsInfoView goodsInfoView = new GoodsInfoView(item.getGoods().getGoodsSN(), item.getGoods().getGoodsName(), item.getGoods().getSalePrice(), item.getOrderGoodsCount());
            goodsInfoViewList.add(goodsInfoView);
        }
        return goodsInfoViewList;
    }
}
```

#### 订单数据库对象示例

```java
/**
 * 订单物理模型
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:30<p>
 * ================================
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_order")
public class OrderDO extends BaseDO {
    /**
     * 订单编号
     */
    @TableField("order_sn")
    private String orderSN;

    /**
     * 客户SN
     */
    @TableField("customer_sn")
    private String customerSN;

    /**
     * 客户昵称
     */
    @TableField("customer_nick_name")
    private String customerNickName;

    /**
     * 客户手机号
     */
    @TableField("customer_phone_number")
    private String customerPhoneNumber;

    /**
     * 订单总金额
     */
    @TableField("order_money")
    private BigDecimal orderMoney;

    /**
     * 订单商品列表
     */
    @TableField("goods_list")
    private String goodsList;

    /**
     * 订单下单时间
     */
    @TableField("place_order_time")
    private LocalDateTime placeOrderTime;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private Integer orderStatus;

}
```

#### 订单Mapper接口示例

```java
/**
 * 订单Mapper接口
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:12<p>
 * ================================
 */
@Mapper
public interface OrderMapper extends BaseMapper<OrderDO> {

}
```

#### 资源网关适配器示例

```java
/**
 * 订单资源网关实现
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:27<p>
 * ================================
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderResourceGatewayAdapter implements OrderResourceGateway {

    /**
     * 商品管理客户端
     */
    private final GoodsManagementClient goodsManagementClient;

    /**
     * 库存客户端
     */
    private final CheckDeductionsStockClient checkDeductionsStockClient;

    /**
     * 校验商品库存
     *
     * @param orderGoods 订单商品
     * @return 库存是否满足下单需求
     */
    @Override
    public Boolean validateGoodsStock(OrderGoods orderGoods) {
        // 调用库存客户端，校验商品库存
        CheckStockIsAvailableClientRequest checkStockIsAvailableClientRequest = new CheckStockIsAvailableClientRequest(orderGoods.getGoods().getGoodsSN(), orderGoods.getOrderGoodsCount());
        R<StockIsAvailableCheckClientResponse> stockIsAvailableCheckClientResponseR = checkDeductionsStockClient.checkStockIsAvailable(checkStockIsAvailableClientRequest);

        // 返回库存是否校验成功
        if (!stockIsAvailableCheckClientResponseR.isSuccess()) {
            log.error("库存校验失败,失败原因：{}, 错误码：{}", stockIsAvailableCheckClientResponseR.getMsg(), stockIsAvailableCheckClientResponseR.getErrorCode());
            return false;
        }
        StockIsAvailableCheckClientResponse data = stockIsAvailableCheckClientResponseR.getData();
        return data.getStockIsAvailable();
    }

    /**
     * 扣减商品库存
     *
     * @param orderAggregateRootEntity 订单聚合根领域对象
     */
    @Override
    public void deductGoodsStock(OrderAggregateRootEntity orderAggregateRootEntity) {
        // 获取商品SN及订单购买商品数量对应关系
        Map<String, Integer> goodsCountMap = orderAggregateRootEntity.getOrderGoods().stream().collect(Collectors.toMap(item -> item.getGoods().getGoodsSN(), OrderGoods::getOrderGoodsCount));
        for (String goodsSN : goodsCountMap.keySet()) {
            // 调用库存客户端，扣减库存
            Integer orderPurchasedGoodsCount = goodsCountMap.get(goodsSN);
            DeductionStockClientRequest deductionStockClientRequest = new DeductionStockClientRequest(goodsSN, orderPurchasedGoodsCount);
            checkDeductionsStockClient.deductionStock(deductionStockClientRequest);
        }
    }

    /**
     * 补全订单商品信息
     *
     * @param orderGoodsList 订单商品列表
     * @return 补全后的订单商品列表
     */
    @Override
    public List<OrderGoods> queryOrderGoodsList(List<OrderGoods> orderGoodsList) {
        // 获取商品业务编号集合
        List<String> goodsSNList = orderGoodsList.stream().map(item -> item.getGoods().getGoodsSN()).collect(Collectors.toList());

        // 调用商品客户端，查询商品信息
        QueryListedGoodsClientRequest queryListedGoodsClientRequest = new QueryListedGoodsClientRequest(goodsSNList);
        // todo 涉及到client调用，用R包装，通过响应码及错误码来判断是否成功以及异常信息
        R<List<ListedGoodsQueryClientResponse>> listedGoodsGainClientResponseR = goodsManagementClient.queryListedGoodsList(queryListedGoodsClientRequest);
        if (!listedGoodsGainClientResponseR.isSuccess()) {
            log.error("调用商品信息返回的错误码： = {}， 错误描述： = {}", listedGoodsGainClientResponseR.getErrorCode(), listedGoodsGainClientResponseR.getMsg());
            return null;
        }
        // 构建商品信息map
        List<ListedGoodsQueryClientResponse> data = listedGoodsGainClientResponseR.getData();
        Map<String, ListedGoodsQueryClientResponse> goodsInfoMap = data.stream().collect(Collectors.toMap(ListedGoodsQueryClientResponse::getGoodsSN, item -> item));

        // 遍历订单商品，构建订单商品信息
        List<OrderGoods> result = new ArrayList<>();
        for (OrderGoods item : orderGoodsList) {
            // 获取商品编号
            String goodsSN = item.getGoods().getGoodsSN();
            // 获取商品信息
            ListedGoodsQueryClientResponse listedGoodsQueryClientResponse = goodsInfoMap.get(goodsSN);
            // 查询商品信息
            Goods goods = new Goods(goodsSN, listedGoodsQueryClientResponse.getGoodsName(), listedGoodsQueryClientResponse.getSalePrice());
            OrderGoods orderGoods = new OrderGoods(goods, item.getOrderGoodsCount());
            result.add(orderGoods);
        }
        // 返回订单商品列表
        return result;
    }
}
```
