---
type: "manual"
description: "生成北向网关remote模块时需要"
---
# 北向网关-远程网关（xx-northbound-remote）规范

## 注意事项

1. 有xxxController类，提供API给前端调用，与前端进行交互, 一个系统用例对应一个Controller类。
2. 进入系统的数据和系统返回的数据，在pl层被定义了结构。
3. 有xxxProvider类，对本上下文的client接口进行实现。
4. 有xxxSubscriber类，接收MQ消息通知，处理这个消息对应触发的业务。

## 重要规范

1. 充分利用已有的项目结构, 禁止创建不必要的项目结构目录或文件.

## 生成后的检验清单

- [ ] 生成新的目录、文件、代码内容后，需要校验生成内容是否符合注意事项

## 示例参考

### 当前模块分层规范

```
com.zz.dingdangmallprd.orderbc.northbound.remote
└── placeorderbiz                     # 核心业务维度划分
    ├── PlaceOrderController.java        # REST-API接口暴露层
    ├── PlaceOrderEventSubscriber.java   # 跨域事件订阅器
    ├── PlaceOrderProvider.java          # client层的RPC服务提供者实现
    └─- package-info.java
```

### 当前模块下的代码内容示例

#### 事件订阅者示例

```java
/**
 * 发送短信通知客户事件订阅
 *
 * <AUTHOR> {author-email}
 * ===========================
 * Date 2024-10-05
 * Time 12:05
 * ===========================
 */
@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(consumerGroup = PlaceOrderAppEventConstant.ORDER_PLACED_APP_EVENT_TOPIC, topic = PlaceOrderAppEventConstant.ORDER_PLACED_APP_EVENT_TOPIC, messageModel = MessageModel.BROADCASTING)
public class SendSmsNoticeCustomerEventSubscriber implements RocketMQListener<String> {

    /**
     * 发送短信命令用例应用服务
     */
    private final SendSmsCommandUseCaseAppService sendSmsCommandUseCaseAppService;

    /**
     * 发送短信通知客户(应用事件订阅)
     *
     * @param sendSmsNoticeCustomerMsg 发送短信通知客户消息
     */
    @Override
    public void onMessage(String sendSmsNoticeCustomerMsg) {
        log.info("监听到发送短信通知客户应用事件 事件内容:{} ", sendSmsNoticeCustomerMsg);
        SendSmsNoticeCustomerRequest sendSmsNoticeCustomerRequest = JSON.parseObject(sendSmsNoticeCustomerMsg, SendSmsNoticeCustomerRequest.class);
        //发送短信
        sendSmsCommandUseCaseAppService.sendSms(sendSmsNoticeCustomerRequest);
    }
}
```

#### API控制器

```java
/**
 * 下单控制器
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:32<p>
 * ================================
 */
@RestController
@AllArgsConstructor
@RequestMapping("/order")
@Api(tags = "下单", value = "下单")
public class PlaceOrderController {
    /**
     * 下单业务服务（命令模式）
     */
    private final PlaceOrderCommandUseCaseAppService placeOrderCommandUseCaseAppService;

    /**
     * 下单业务服务（查询模式）
     */
    private final PlaceOrderQueryUseCaseAppService placeOrderQueryUseCaseAppService;

    /**
     * 下单API
     *
     * @param placeOrderRequest 下单请求对象
     * @return 订单号
     */
    @PostMapping("/place-order")
    @ApiOperation(value = "下单")
    public R<Void> placeOrder(@RequestBody @Valid PlaceOrderRequest placeOrderRequest) {
        // 下单
        placeOrderCommandUseCaseAppService.placeOrder(placeOrderRequest);
        return R.success("下单成功");
    }

    /**
     * 查询订单列表
     *
     * @return 订单视图列表
     */
    @PostMapping("/query-order-list")
    @ApiOperation(value = "查询订单列表")
    public R<List<OrderListQueryView>> queryOrderList(@RequestBody QueryOrderListRequest queryOrderListRequest) {
        // 查询订单列表
        return R.data(placeOrderQueryUseCaseAppService.queryOrderList(queryOrderListRequest));
    }

}
```

#### client层功能提供者

```java
/**
 * client层下单功能提供者
 *
 * <AUTHOR> <p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:32<p>
 * ================================
 */
@Controller
@AllArgsConstructor
public class GoodsManagementProvider implements GoodsManagementClient {

    /**
     * 查询上架商品信息应用服务
     */
    private final GoodsManagementQueryUseCaseAppService goodsManagementQueryUseCaseAppService;

    /**
     * 查询上架商品信息
     *
     * @param queryListedGoodsClientRequest 查询上架商品信息
     * @return 上架商品信息
     */
    @Override
    public R<List<ListedGoodsQueryClientResponse>> queryListedGoodsList(QueryListedGoodsClientRequest queryListedGoodsClientRequest) {
        QueryListedGoodsRequest queryListedGoodsRequest = GoodsManagementMapper.INSTANCE.toQueryListedGoodsRequest(queryListedGoodsClientRequest);
        // 查询上架商品信息
        List<ListedGoodsQueryResponse> listedGoodsQueryResponseList = goodsManagementQueryUseCaseAppService.queryListedGoodsList(queryListedGoodsRequest);
        return R.data(GoodsManagementMapper.INSTANCE.toListedGoodsQueryClientResponse(listedGoodsQueryResponseList));
    }
}
```