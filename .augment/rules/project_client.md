---
type: "manual"
description: "生成client客户端模块时需要"
---
# 客户端访问层（xx-client）规范

## 注意事项

1. 客户端层负责提供外部系统调用的能力, 使用pl进行数据传输.
2. 客户端的pl的类命名为XxxClientRequest和XxxClientResponse, 相对于本地网关的pl增加了Client前缀.

## 重要规范

1. 充分利用已有的项目结构, 禁止创建不必要的项目结构目录或文件.

## 生成后的检验清单

- [ ] 生成新的目录、文件、代码内容后，需要校验生成内容是否符合注意事项
- [ ] 防御性字段：DTO中金额字段是否使用Decimal类型
- [ ] 业务特征验证：所有Client结尾的类是否在[业务服务biz]包下
- [ ] ...

## 示例参考

### 当前模块分层规范

```
com.zz.dingdangmallprd.orderbc.client
├── placeorderbiz  # 按业务服务/系统用例划分的包
│   ├── constant    # 常量定义
│   ├── pl          # 消息契约模型
│   │   ├── PalceOrderClientRequest.java  # 客户端请求对象
│   │   └── OrderPlacedClientResponse.java  # 客户端请求对象
│   └── PlaceOrderClient.java   # 用例客户端接口
└── package-info.java

```

### 当前模块下的代码内容示例

#### 常量定义

```java
/**
 * 已下单应用事件常量类
 *
 * <AUTHOR> {author-email}
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 12:46<p>
 * ================================
 */
public interface PlaceOrderAppEventConstant {
    /**
     * 已下单应用事件队列
     */
    String ORDER_PLACED_APP_EVENT_TOPIC = "orderPlacedAppEventTopic";
}
```

#### 请求对象

```java
/**
 * 查询上架商品请求对象
 *
 * <AUTHOR>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:39<p>
 * ================================
 */
@Data
@AllArgsConstructor
public class QueryListedGoodsClientRequest {
    /**
     * 商品业务编号列表
     */
    @NotEmpty(message = "商品业务编号列表不能为空")
    private List<String> goodsSNList;
}
```

#### 响应对象

```java
/**
 * 商品信息响应对象
 *
 * <AUTHOR>
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 10:08<p>
 * ================================
 */
@Data
public class ListedGoodsQueryClientResponse {
    /**
     * 商品业务编号
     */
    private String goodsSN;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品价格
     */
    private BigDecimal salePrice;
}
```

#### 客户端接口

```java
/**
 * 商品管理客户端
 * <AUTHOR>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:22<p>
 * ================================
 */
public interface GoodsManagementClient {

    /**
     * 查询上架的商品信息
     * @param gainGoodsRequest 商品业务编号列表
     * @return 商品信息返回
     */
    R<List<ListedGoodsQueryClientResponse>> queryListedGoodsList(QueryListedGoodsClientRequest gainGoodsRequest);
}
```