---
type: "manual"
description: "生成北向网关local模块时需要"
---
# 北向网关-本地网关（xx-northbound-local）规范

## 注意事项

1. 应用服务负责领业务的流程编排（调用其他上下文的应用服务，或者调用本身上下文下的领域服务）
2. 使用pl进行数据传输, 根据不同的业务划分, 每个业务都有自己的请求响应对象, 例如增删改查应该有四套请求响应对象.
3. 使用MapStruct框架进行对象转换时(Assembler/Mapper/Converter), 直接使用对应接口的的INSTANCE实例, 不需要进行依赖注入。例如: OrderConverter.INSTANCE.toDO(OrderEntity);
4. 对于MapStruct框架接口中的转换方法, 不需要使用@Mapping字段映射注解进行说明, 除非转换的字段需要特殊处理.避免重复定义、充分利用uses进行自动转换.
5. 每个应用服务都需要处理事务边界
6. 每一个（业务服务/系统用例）都需要遵守代码规范（CQRS，命令和查询职责分离）, 例如：下单的（业务服务/系统用例），需要创建PlaceOrderCommandUseCaseService（对应下单系统用例的操作行为）和PlaceOrderQueryUseCaseService（对应下单系统用例的查询行为）.
7. 查询的仓储接口创建在北向网关-本地网关
8. 可能需要的导包有:
  - import com.zz.core.ddd.common.mapstruct.CommonMapping;

## 重要规范

1. 充分利用已有的项目结构, 禁止创建不必要的项目结构目录或文件.
2. 业务服务/系统用例的代码构建，遵循：CQRS（命令和查询职责分离）规范，命令职责的代码编写与业务逻辑相关的数据库操作，查询职责的代码编写查询或者与实际业务逻辑无关的更新缓存/同步数据等数据库操作
3. 当前模块的pl层存放着这个上下文的请求和响应对象，请求对象结构为：xxxRequest，响应对象有3种结构类型：xxResult（调用命令职责返回的结果）、xxxResponse（调用当前上下文client能力后返回的结果）、xxxxView（调用查询职责获取的结果）

## 生成后的检验清单

- [ ] 生成新的目录、文件、代码内容后，需要校验生成内容是否符合注意事项
- [ ] 生成的消息协议层是否遵循：当前模块的pl层存放着这个上下文的请求和响应对象，请求对象结构为：xxxRequest，响应对象有3种结构类型：xxResult（调用命令职责返回的结果）、xxxResponse（调用当前上下文client能力后返回的结果）、xxxxView（调用查询职责获取的结果）
- [ ] 生成的

## 示例参考

### 当前模块分层规范

```
orderbc-northbound-local
└── com.zz.dingdangmallprd.orderbc.northbound.local
    └── placeorderbiz                     # 核心业务维度划分
        ├── pl                            # 消息协议层（Protocol Layer）
        │   ├── PlaceOrderRequest.java       # 应用层请求DTO
        │   ├── OrderPlacedResult.java       # 命令型响应（含交易号等核心结果）  
        │   ├── OrderPlacedResponse.java     # 上下文扩展响应体（可选）
        │   └── OrderPlacedView.java         # 视图对象（VO前端适配）
        │
        ├── appevent                       # 应用事件定义
        │   └── OrderPlacedAppEvent.java      # （带有版本号的应用事件） 
        │
        ├── PlaceOrderAppEventPublisher.java  # 事件发布接口  
        ├── PlaceOrderAssembler.java        # DTO与DO转换器  
        ├── PlaceOrderCommandUseCaseAppService.java # 命令型业务入口
        ├── PlaceOrderQueryRepository.java  # 查询存储抽象  
        ├── PlaceOrderQueryUseCaseAppService.java # 查询型业务入口
        └── package-info.java
```
    
### 当前模块下的代码内容示例

#### 下单请求对象

```java
/**
 * 下单请求对象
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:39<p>
 * ================================
 */
@Data
@ApiModel(value = "下单请求")
public class PlaceOrderRequest {
    /**
     * 订单商品信息
     */
    @NotEmpty(message = "订单商品信息不能为空")
    @ApiModelProperty(value = "商品列表")
    private List<GoodsInfoRequest> goodsList;
}
```

#### 下单结果对象

```java
/**
 * 下单结果对象
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:39<p>
 * ================================
 */
@Data
@ApiModel(value = "下单结果")
public class OrderPlaceResult {
    /**
     * 订单号
     */
     @ApiModelProperty(value = "订单编号")
     private String orderSN;
}
```

#### 订单查询视图对象

```java
/**
 * 订单查询视图对象
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 12:42<p>
 * ================================
 */
@Data
@ApiModel(value = "查询订单视图")
public class OrderListQueryView {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderSN;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerSN;

    /**
     * 客户昵称
     */
    @ApiModelProperty(value = "客户昵称")
    private String customerNickName;

    /**
     * 客户手机号
     */
    @ApiModelProperty(value = "客户手机号")
    private String customerPhoneNumber;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderMoney;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<GoodsInfoView> goodsList;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private LocalDateTime placeOrderTime;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;
}
```

#### 已下单应用事件

```java
/**
 * 已下单应用事件
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/5<p>
 * Time: 10:31<p>
 * ================================
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderPlacedAppEvent {
    /**
     * 订单业务编号
     */
    private String orderSN;

    /**
     * 客户业务编号
     */
    private String customerSN;

    /**
     * 下单时间
     */
    private LocalDateTime placeOrderTime;

    /**
     * 商品信息
     */
    private List<OrderGoods> orderGoodsList;

    /**
     * 构建已下单应用事件
     *
     * @param order 订单聚合根
     * @return 已下单应用事件
     */
    public static OrderPlacedAppEvent from(OrderAggregateRootEntity order) {
        return OrderPlacedAppEvent.builder()
                .customerSN(order.getCustomer().getCustomerSN())
                .orderSN(order.getOrderSN().getValue())
                .placeOrderTime(order.getPlaceOrderTime().getValue())
                .orderGoodsList(order.getOrderGoods())
                .build();
    }
}
```

#### 订单事件发布者（接口）

```java
/**
 * 订单事件发布者（接口）
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:24<p>
 * ================================
 */
public interface PlaceOrderAppEventPublisher {

    /**
     * 发布下单通知客户事件
     *
     * @param order 订单聚合根实体
     */
    void publishOrderPlacedAppEvent(OrderPlacedAppEvent order);
}
```

#### 下单业务服务的参数装配器

```java
/**
 * 下单业务服务的参数装配器
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:37<p>
 * ================================
 */
@Mapper(uses = {OrderMapping.class, CommonMapping.class})
public interface PlaceOrderAssembler {

    PlaceOrderAssembler INSTANCE = Mappers.getMapper(PlaceOrderAssembler.class);

    /**
     * 请求参数转换为领域对象
     *
     * @param placeOrderRequest 请求参数
     * @return 领域对象
     */
    @Mappings({
            @Mapping(target = "orderGoods", source = "goodsList", qualifiedByName = "toOrderGoods")
    })
    OrderAggregateRootEntity toOrderAggregateRootEntity(PlaceOrderRequest placeOrderRequest);

    /**
     * 商品信息转换为订单商品值对象
     *
     * @param goodsList 商品信息
     * @return 领域对象
     */
    @Named("toOrderGoods")
    default List<OrderGoods> toOrderGoods(List<GoodsInfoRequest> goodsList) {
        List<OrderGoods> orderGoodsList = new ArrayList<>();
        for (GoodsInfoRequest goodsInfoRequest : goodsList) {
            Goods goods = new Goods(goodsInfoRequest.getGoodsSN(), goodsInfoRequest.getGoodsName(), goodsInfoRequest.getSalePrice());
            OrderGoods orderGoods = new OrderGoods(goods, goodsInfoRequest.getOrderGoodsCount());
            orderGoodsList.add(orderGoods);
        }
        return orderGoodsList;
    }

}
```

#### 下单业务服务（操作、命令职责)

```java
/**
 *  下单业务服务（操作、命令职责)
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:36<p>
 * ================================
 */
@Service
@AllArgsConstructor
public class PlaceOrderCommandUseCaseAppService {
    /**
     * 订单 领域服务
     */
    private final OrderDomainService orderDomainService;

    /**
     * 事件发布器
     */
    private final PlaceOrderAppEventPublisher placeOrderAppEventPublisher;

    /**
     * 下单
     *
     * @param placeOrderRequest 下单请求
     */
    public void placeOrder(PlaceOrderRequest placeOrderRequest) {
        // 下单请求消息模型转换为订单领域对象
        OrderAggregateRootEntity orderAggregateRootEntity = PlaceOrderAssembler.INSTANCE.toOrderAggregateRootEntity(placeOrderRequest);

        // 省略商品下单分布式锁

        // 完成商品下单
        this.orderDomainService.placeOrder(orderAggregateRootEntity);

        // 发布已下单应用事件
        OrderPlacedAppEvent orderPlacedAppEvent = OrderPlacedAppEvent.from(orderAggregateRootEntity);
        this.placeOrderAppEventPublisher.publishOrderPlacedAppEvent(orderPlacedAppEvent);
    }

}
```

#### 下单业务服务（查询职责)

```java
/**
 * 下单业务服务（查询职责)
 *
 * <AUTHOR> {author-name} {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:36<p>
 * ================================
 */
@Component
@AllArgsConstructor
public class PlaceOrderQueryUseCaseAppService {
    /**
     * 订单查询仓储
     */
    private final PlaceOrderQueryRepository placeOrderQueryRepository;

    /**
     * 查询订单列表
     *
     * @param queryOrderListRequest 查询参数列表请求对象
     * @return 订单视图列表
     */
    public List<OrderListQueryView> queryOrderList(QueryOrderListRequest queryOrderListRequest) {
        // 查询订单视图列表
        return placeOrderQueryRepository.queryOrderList(queryOrderListRequest);
    }
}
```


#### 下单业务服务（查询职责）的查询仓储接口

```java
/**
 * 订下单业务服务（查询职责）的查询仓储接口
 *
 * 需要被南向网关对应的领域聚合的查询仓储适配器所实现
 *
 * <AUTHOR> {author-email}<p>
 * ================================<p>
 * Date: 2024/10/4<p>
 * Time: 11:23<p>
 * ================================
 */
public interface PlaceOrderQueryRepository {

    /**
     * 同步ElasticSearch和数据库
     *
     * @param orderAggregateRootEntity 订单聚合根实体
     */
    void syncOrderToElasticSearch(OrderAggregateRootEntity orderAggregateRootEntity);

    /**
     * 查询订单列表
     *
     * @return 订单聚合根实体
     */
    List<OrderListQueryView> queryOrderList(QueryOrderListRequest queryOrderListRequest);
}
```
